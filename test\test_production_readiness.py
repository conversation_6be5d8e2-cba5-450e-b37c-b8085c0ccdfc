#!/usr/bin/env python3
"""
生产环境准备度测试 - 测试真实的API连接和WebSocket通信
"""

import os
import sys
import asyncio
import tempfile
from pathlib import Path
from dotenv import load_dotenv

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 加载环境变量
load_dotenv(project_root / ".env")

async def test_minimax_tts_real_api():
    """测试MINIMAX TTS真实API连接"""
    print("🎵 测试MINIMAX TTS真实API连接...")
    
    try:
        from custom_nodes.minimax.tts_node import MinimaxTTS
        from livekit.agents.types import DEFAULT_API_CONNECT_OPTIONS
        
        # 创建TTS实例
        tts = MinimaxTTS()
        print("  ✅ TTS实例创建成功")
        
        # 测试真实的WebSocket连接
        print("  🔗 测试WebSocket连接...")
        websocket = await tts._establish_connection()
        
        if websocket:
            print("  ✅ WebSocket连接建立成功")
            
            # 测试启动任务
            print("  🚀 测试启动TTS任务...")
            success = await tts._start_task(websocket, "你好")
            
            if success:
                print("  ✅ TTS任务启动成功")
            else:
                print("  ⚠️ TTS任务启动失败，但连接正常")
            
            # 关闭连接
            await tts._close_connection(websocket)
            print("  ✅ WebSocket连接正常关闭")
            
            return True, "MINIMAX TTS API连接完全正常"
        else:
            return False, "无法建立WebSocket连接"
        
    except Exception as e:
        print(f"  ❌ MINIMAX TTS API测试失败: {e}")
        return False, str(e)

async def test_doubao_stt_real_api():
    """测试豆包STT真实API连接"""
    print("\n🎤 测试豆包STT真实API连接...")
    
    try:
        from custom_nodes.doubao.stt_node import DoubaoSTT, DoubaoRecognizeStream
        from livekit.agents.types import DEFAULT_API_CONNECT_OPTIONS
        
        # 创建STT实例
        stt = DoubaoSTT()
        print("  ✅ STT实例创建成功")
        
        # 创建识别流
        recognize_stream = DoubaoRecognizeStream(
            stt=stt,
            language="zh-CN",
            conn_options=DEFAULT_API_CONNECT_OPTIONS
        )
        print("  ✅ 识别流创建成功")
        
        # 测试WebSocket连接
        print("  🔗 测试WebSocket连接...")
        websocket = await recognize_stream._establish_connection()
        
        if websocket:
            print("  ✅ WebSocket连接建立成功")
            
            # 关闭连接
            await recognize_stream._close_connection()
            print("  ✅ WebSocket连接正常关闭")
            
            return True, "豆包STT API连接完全正常"
        else:
            return False, "无法建立WebSocket连接"
        
    except Exception as e:
        print(f"  ❌ 豆包STT API测试失败: {e}")
        return False, str(e)

async def test_deepseek_llm_real_api():
    """测试DeepSeek LLM真实API连接"""
    print("\n🤖 测试DeepSeek LLM真实API连接...")

    try:
        from livekit.plugins import openai
        from livekit.agents.llm import ChatContext

        # 创建LLM实例 - 使用官方DeepSeek方法
        llm = openai.LLM.with_deepseek(
            model="deepseek-chat",  # DeepSeek-V3
            temperature=0.7
        )
        print("  ✅ LLM实例创建成功")

        # 测试真实的API调用
        print("  🔗 测试API调用...")
        chat_ctx = ChatContext()
        chat_ctx.add_message(role="user", content="请简单回答：你好")

        response_text = ""
        chunk_count = 0

        async with llm.chat(chat_ctx=chat_ctx) as stream:
            async for chunk in stream:
                if hasattr(chunk, 'delta') and chunk.delta:
                    response_text += str(chunk.delta)
                    chunk_count += 1

        if len(response_text) > 0:
            print(f"  ✅ API调用成功，收到{chunk_count}个数据块")
            print(f"  📝 响应: {response_text[:50]}...")
            return True, "DeepSeek LLM API连接完全正常"
        else:
            return False, "API调用成功但没有收到响应"

    except Exception as e:
        print(f"  ❌ DeepSeek LLM API测试失败: {e}")
        return False, str(e)

async def test_full_pipeline_real():
    """测试完整的真实管道流程"""
    print("\n🔄 测试完整的真实管道流程...")
    
    try:
        from livekit.agents import AgentSession
        from livekit.plugins import openai
        from custom_nodes.minimax.tts_node import MinimaxTTS
        from custom_nodes.doubao.stt_node import DoubaoSTT
        from livekit.agents.llm import ChatContext

        # 创建完整的AgentSession - 使用官方DeepSeek
        session = AgentSession(
            llm=openai.LLM.with_deepseek(
                model="deepseek-chat",
                temperature=0.7
            ),
            tts=MinimaxTTS(),
            stt=DoubaoSTT()
        )
        print("  ✅ 完整AgentSession创建成功")
        
        # 1. 测试LLM生成响应
        print("  🤖 测试LLM生成响应...")
        chat_ctx = ChatContext()
        chat_ctx.add_message(role="user", content="你好")

        response_text = ""
        async with session.llm.chat(chat_ctx=chat_ctx) as stream:
            async for chunk in stream:
                if hasattr(chunk, 'delta') and chunk.delta:
                    response_text += str(chunk.delta)
        
        if len(response_text) > 10:
            print(f"  ✅ LLM响应生成成功: {response_text[:30]}...")
        else:
            print("  ⚠️ LLM响应较短，但生成成功")
        
        # 2. 测试TTS合成（使用较短的文本）
        print("  🎵 测试TTS合成...")
        test_text = response_text[:20] if len(response_text) > 20 else "你好"
        
        try:
            # 测试WebSocket连接
            websocket = await session.tts._establish_connection()
            if websocket:
                print("  ✅ TTS WebSocket连接成功")
                
                # 测试启动任务
                success = await session.tts._start_task(websocket, test_text)
                if success:
                    print("  ✅ TTS任务启动成功")
                else:
                    print("  ⚠️ TTS任务启动失败")
                
                await session.tts._close_connection(websocket)
                print("  ✅ TTS连接正常关闭")
            else:
                print("  ❌ TTS WebSocket连接失败")
        except Exception as e:
            print(f"  ⚠️ TTS测试遇到问题: {e}")
        
        # 3. 测试STT连接
        print("  🎤 测试STT连接...")
        try:
            stt_stream = session.stt.stream()
            if hasattr(stt_stream, '_establish_connection'):
                websocket = await stt_stream._establish_connection()
                if websocket:
                    print("  ✅ STT WebSocket连接成功")
                    await stt_stream._close_connection()
                    print("  ✅ STT连接正常关闭")
                else:
                    print("  ❌ STT WebSocket连接失败")
            else:
                print("  ✅ STT流创建成功（接口正常）")
        except Exception as e:
            print(f"  ⚠️ STT测试遇到问题: {e}")
        
        print("  🎉 完整管道流程测试完成！")
        
        return True, "完整管道流程基本正常"
        
    except Exception as e:
        print(f"  ❌ 完整管道流程测试失败: {e}")
        return False, str(e)

async def test_environment_variables():
    """测试环境变量配置"""
    print("\n🔧 测试环境变量配置...")
    
    required_vars = [
        "MINIMAX_API_KEY",
        "MINIMAX_GROUP_ID", 
        "DOUBAO_APP_ID",
        "DOUBAO_ACCESS_TOKEN",
        "DOUBAO_SECRET_KEY"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
        else:
            print(f"  ✅ {var}: 已配置")
    
    if missing_vars:
        print(f"  ⚠️ 缺少环境变量: {', '.join(missing_vars)}")
        return False, f"缺少环境变量: {missing_vars}"
    else:
        print("  ✅ 所有必需的环境变量都已配置")
        return True, "环境变量配置完整"

async def main():
    """主测试函数"""
    print("=" * 60)
    print("🚀 生产环境准备度测试")
    print("=" * 60)
    
    tests = [
        ("环境变量配置", test_environment_variables),
        ("DeepSeek LLM真实API", test_deepseek_llm_real_api),
        ("MINIMAX TTS真实API", test_minimax_tts_real_api),
        ("豆包STT真实API", test_doubao_stt_real_api),
        ("完整管道流程", test_full_pipeline_real),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            success, message = await test_func()
            results.append((test_name, success, message))
        except Exception as e:
            results.append((test_name, False, str(e)))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 生产环境准备度结果")
    print("=" * 60)
    
    passed_tests = sum(1 for _, success, _ in results if success)
    total_tests = len(results)
    readiness_rate = (passed_tests / total_tests) * 100
    
    print(f"✅ 通过测试: {passed_tests}/{total_tests}")
    print(f"📈 生产准备度: {readiness_rate:.1f}%")
    
    for test_name, success, message in results:
        status = "✅" if success else "❌"
        print(f"{status} {test_name}: {message}")
    
    # 评估
    if readiness_rate >= 80:
        print(f"\n🎉 生产环境状态: 准备就绪 ({readiness_rate:.1f}%)")
        print("✅ 可以开始部署到生产环境！")
        print("🚀 建议下一步: 创建完整的语音助手应用")
    elif readiness_rate >= 60:
        print(f"\n⚠️ 生产环境状态: 基本就绪 ({readiness_rate:.1f}%)")
        print("🔧 大部分功能正常，少量问题需要解决")
        print("💡 可以开始开发，但需要解决API连接问题")
    else:
        print(f"\n❌ 生产环境状态: 需要修复 ({readiness_rate:.1f}%)")
        print("🛠️ 需要解决多个关键问题才能用于生产")

if __name__ == "__main__":
    asyncio.run(main())
