#!/usr/bin/env python3
"""
DeepSeek LLM 自定义实现 - 严格按照官方demo，支持流式传输，完全兼容LiveKit Agent
"""

import os
import asyncio
from typing import Optional, AsyncIterable, List, Dict, Any
from dotenv import load_dotenv

# 加载.env文件
load_dotenv()

from livekit.agents import llm, utils
from livekit.agents.llm import ChatContext, LLMStream, ChatMessage, ChatRole
from livekit.agents.types import DEFAULT_API_CONNECT_OPTIONS, APIConnectOptions

import logging
logger = logging.getLogger(__name__)

try:
    from openai import OpenAI, AsyncOpenAI
except ImportError:
    raise ImportError("请安装openai库: pip install openai")


class DeepSeekLLM(llm.LLM):
    """DeepSeek LLM 自定义实现 - 严格按照官方标准"""

    def __init__(
        self,
        api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        model: str = "deepseek-chat",
        temperature: float = 0.7,
        max_tokens: int = 1024,
    ):
        super().__init__()

        # 从环境变量或参数获取配置
        self._api_key = api_key or os.getenv("DEEPSEEK_API_KEY")
        self._base_url = base_url or os.getenv("DEEPSEEK_BASE_URL", "https://api.deepseek.com")
        self._model = model
        self._temperature = temperature
        self._max_tokens = max_tokens

        if not self._api_key:
            raise ValueError("DeepSeek API密钥是必需的。请设置DEEPSEEK_API_KEY环境变量或传递api_key参数")

        # 创建OpenAI客户端 - 严格按照官方demo
        self._client = AsyncOpenAI(
            api_key=self._api_key,
            base_url=self._base_url
        )

        logger.info(f"DeepSeek LLM初始化成功 - 模型: {self._model}, 端点: {self._base_url}")

    @property
    def model(self) -> str:
        """返回模型名称"""
        return self._model

    def chat(
        self,
        *,
        chat_ctx: ChatContext,
        conn_options: APIConnectOptions = DEFAULT_API_CONNECT_OPTIONS,
        fnc_ctx: Optional[Any] = None,
        temperature: Optional[float] = None,
        n: int = 1,
        parallel_tool_calls: bool = True,
    ) -> "DeepSeekLLMStream":
        """创建聊天流 - 完全兼容LiveKit标准"""
        return DeepSeekLLMStream(
            llm=self,
            chat_ctx=chat_ctx,
            conn_options=conn_options,
            fnc_ctx=fnc_ctx,
            temperature=temperature or self._temperature,
            n=n,
            parallel_tool_calls=parallel_tool_calls,
        )


class DeepSeekLLMStream(LLMStream):
    """DeepSeek LLM流 - 支持流式传输"""

    def __init__(
        self,
        llm: DeepSeekLLM,
        chat_ctx: ChatContext,
        conn_options: APIConnectOptions,
        fnc_ctx: Optional[Any],
        temperature: float,
        n: int,
        parallel_tool_calls: bool,
    ):
        super().__init__(llm, chat_ctx=chat_ctx, tools=[], conn_options=conn_options)
        self._llm = llm
        self._chat_ctx = chat_ctx
        self._conn_options = conn_options
        self._fnc_ctx = fnc_ctx
        self._temperature = temperature
        self._n = n
        self._parallel_tool_calls = parallel_tool_calls
        self._response_stream = None

    async def __aenter__(self) -> "DeepSeekLLMStream":
        """进入异步上下文"""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb) -> None:
        """退出异步上下文"""
        if self._response_stream:
            try:
                await self._response_stream.aclose()
            except Exception as e:
                logger.warning(f"关闭DeepSeek响应流时出错: {e}")

    def __aiter__(self) -> AsyncIterable[llm.ChatChunk]:
        """异步迭代器"""
        return self._stream_chat()

    async def _run(self) -> None:
        """实现抽象方法_run"""
        async for chunk in self._stream_chat():
            self._event_ch.send_nowait(chunk)

    async def _stream_chat(self) -> AsyncIterable[llm.ChatChunk]:
        """流式聊天实现 - 严格按照官方demo"""
        try:
            # 转换ChatContext为OpenAI格式
            messages = self._convert_chat_context_to_messages(self._chat_ctx)

            logger.info(f"DeepSeek流式聊天开始 - 消息数: {len(messages)}")

            # 创建流式请求 - 严格按照官方demo
            self._response_stream = await self._llm._client.chat.completions.create(
                model=self._llm._model,
                messages=messages,
                max_tokens=self._llm._max_tokens,
                temperature=self._temperature,
                stream=True,  # 启用流式传输
                n=self._n,
            )

            # 处理流式响应
            async for chunk in self._response_stream:
                if chunk.choices and len(chunk.choices) > 0:
                    choice = chunk.choices[0]
                    
                    if choice.delta and choice.delta.content:
                        # 发送内容块
                        yield llm.ChatChunk(
                            request_id=utils.shortuuid(),
                            choices=[
                                llm.Choice(
                                    delta=llm.ChoiceDelta(
                                        content=choice.delta.content,
                                        role=ChatRole.ASSISTANT,
                                    ),
                                    index=choice.index,
                                )
                            ],
                        )

                    # 检查是否完成
                    if choice.finish_reason:
                        logger.info(f"DeepSeek流式聊天完成 - 原因: {choice.finish_reason}")
                        break

        except Exception as e:
            logger.error(f"DeepSeek流式聊天错误: {e}")
            raise

    def _convert_chat_context_to_messages(self, chat_ctx: ChatContext) -> List[Dict[str, str]]:
        """将ChatContext转换为OpenAI消息格式"""
        messages = []
        
        for msg in chat_ctx.messages:
            if isinstance(msg, ChatMessage):
                # 转换角色
                role = "user"
                if msg.role == ChatRole.ASSISTANT:
                    role = "assistant"
                elif msg.role == ChatRole.SYSTEM:
                    role = "system"
                elif msg.role == ChatRole.TOOL:
                    role = "tool"
                
                messages.append({
                    "role": role,
                    "content": msg.content
                })
        
        return messages


# 便捷函数
def create_deepseek_llm(
    api_key: Optional[str] = None,
    model: str = "deepseek-chat",
    temperature: float = 0.7,
    max_tokens: int = 1024,
) -> DeepSeekLLM:
    """创建DeepSeek LLM实例的便捷函数"""
    return DeepSeekLLM(
        api_key=api_key,
        model=model,
        temperature=temperature,
        max_tokens=max_tokens,
    )


# 测试函数
async def test_deepseek_llm():
    """测试DeepSeek LLM"""
    try:
        # 创建LLM实例
        llm_instance = create_deepseek_llm()
        
        # 创建聊天上下文
        chat_ctx = ChatContext()
        chat_ctx.add_message(role="system", content="You are a helpful assistant")
        chat_ctx.add_message(role="user", content="你好，请简单介绍一下自己")
        
        # 测试流式聊天
        print("🤖 DeepSeek LLM流式测试开始...")
        response_text = ""
        
        async with llm_instance.chat(chat_ctx=chat_ctx) as stream:
            async for chunk in stream:
                if chunk.choices and len(chunk.choices) > 0:
                    choice = chunk.choices[0]
                    if choice.delta and choice.delta.content:
                        content = choice.delta.content
                        response_text += content
                        print(content, end='', flush=True)
        
        print(f"\n✅ DeepSeek LLM测试成功")
        print(f"📝 完整响应: {response_text}")
        
        return True
        
    except Exception as e:
        print(f"❌ DeepSeek LLM测试失败: {e}")
        return False


if __name__ == "__main__":
    asyncio.run(test_deepseek_llm())
