#!/usr/bin/env python3

import asyncio
import json
import ssl
import os
import time
import uuid
from typing import AsyncIterable, Optional
import websockets
from io import BytesIO
import numpy as np
from pydub import AudioSegment
from dotenv import load_dotenv

# 加载.env文件
load_dotenv()

from livekit import rtc
from livekit.agents import tts, utils
from livekit.agents.tts import SynthesizedAudio, TTSCapabilities, AudioEmitter
from livekit.agents.types import DEFAULT_API_CONNECT_OPTIONS, APIConnectOptions

import logging
logger = logging.getLogger(__name__)


class MinimaxTTS(tts.TTS):
    """MINIMAX TTS 自定义实现"""
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        group_id: Optional[str] = None,
        model: str = "speech-02-hd",
        voice_id: str = "male-qn-qingse",
        emotion: str = "happy",
        speed: float = 1.0,
        vol: float = 1.0,
        pitch: float = 0.0,
        sample_rate: int = 32000,
        bitrate: int = 128000,
        format: str = "mp3",
        channel: int = 1,
    ):
        super().__init__(
            capabilities=tts.TTSCapabilities(
                streaming=True,
            ),
            sample_rate=sample_rate,
            num_channels=channel,
        )
        
        # 从环境变量或参数获取配置
        self._api_key = api_key or os.getenv("MINIMAX_API_KEY")
        self._group_id = group_id or os.getenv("MINIMAX_GROUP_ID")
        
        if not self._api_key:
            raise ValueError("MINIMAX API key is required")
        if not self._group_id:
            raise ValueError("MINIMAX Group ID is required")
        
        # TTS配置
        self._model = model
        self._voice_id = voice_id
        self._emotion = emotion
        self._speed = speed
        self._vol = vol
        self._pitch = pitch
        self._sample_rate = sample_rate
        self._bitrate = bitrate
        self._format = format
        self._channel = channel
        
        # WebSocket配置
        self._ws_url = "wss://api.minimaxi.com/ws/v1/t2a_v2"
        self._ssl_context = ssl.create_default_context()
        self._ssl_context.check_hostname = False
        self._ssl_context.verify_mode = ssl.CERT_NONE

    @property
    def api_key(self) -> str:
        """获取API密钥"""
        return self._api_key

    @property
    def group_id(self) -> str:
        """获取Group ID"""
        return self._group_id
    
    async def _establish_connection(self) -> Optional[websockets.WebSocketServerProtocol]:
        """建立WebSocket连接"""
        headers = {"Authorization": f"Bearer {self._api_key}"}
        
        try:
            ws = await websockets.connect(
                self._ws_url, 
                additional_headers=headers, 
                ssl=self._ssl_context
            )
            
            # 等待连接确认
            connected = json.loads(await ws.recv())
            if connected.get("event") == "connected_success":
                logger.info("MINIMAX WebSocket连接成功")
                return ws
            else:
                logger.error(f"MINIMAX连接失败: {connected}")
                return None
                
        except Exception as e:
            logger.error(f"MINIMAX WebSocket连接异常: {e}")
            return None
    
    async def _start_task(self, websocket, text: str) -> bool:
        """发送任务开始请求"""
        start_msg = {
            "event": "task_start",
            "model": self._model,
            "voice_setting": {
                "voice_id": self._voice_id,
                "speed": int(self._speed),      # 转换为int
                "vol": int(self._vol),          # 转换为int
                "pitch": int(self._pitch),      # 转换为int
                "emotion": self._emotion
            },
            "audio_setting": {
                "sample_rate": self._sample_rate,
                "bitrate": self._bitrate,
                "format": self._format,
                "channel": self._channel
            }
        }

        try:
            logger.info(f"Sending start task message: {json.dumps(start_msg, indent=2)}")
            await websocket.send(json.dumps(start_msg))

            response_text = await websocket.recv()
            logger.info(f"Received start task response: {response_text}")

            response = json.loads(response_text)
            success = response.get("event") == "task_started"

            if not success:
                logger.error(f"Task start failed. Response: {response}")

            return success
        except Exception as e:
            logger.error(f"MINIMAX任务启动失败: {e}")
            return False
    
    async def _continue_task(self, websocket, text: str) -> str:
        """发送继续请求并收集音频数据 - 严格按照官方demo"""
        try:
            await websocket.send(json.dumps({
                "event": "task_continue",
                "text": text
            }))

            audio_chunks = []
            chunk_counter = 1

            while True:
                response_text = await websocket.recv()
                response = json.loads(response_text)

                if "data" in response and "audio" in response["data"]:
                    audio = response["data"]["audio"]
                    logger.info(f"音频块 #{chunk_counter}, 编码长度: {len(audio)} 字节")
                    audio_chunks.append(audio)
                    chunk_counter += 1

                if response.get("is_final"):
                    break

            return "".join(audio_chunks)

        except Exception as e:
            logger.error(f"MINIMAX continue task失败: {e}")
            return ""

    async def _synthesize_text(self, websocket, text: str) -> bytes:
        """合成文本并收集音频数据"""
        try:
            # 使用continue_task获取音频数据
            await websocket.send(json.dumps({
                "event": "task_continue",
                "text": text
            }))
            
            audio_chunks = []
            while True:
                response = json.loads(await websocket.recv())
                
                if "data" in response and "audio" in response["data"]:
                    audio_hex = response["data"]["audio"]
                    audio_chunks.append(audio_hex)
                
                if response.get("is_final"):
                    break
            
            # 将十六进制字符串转换为字节
            hex_audio = "".join(audio_chunks)
            return bytes.fromhex(hex_audio)
            
        except Exception as e:
            logger.error(f"MINIMAX文本合成失败: {e}")
            return b""
    
    async def _close_connection(self, websocket):
        """关闭连接"""
        try:
            await websocket.send(json.dumps({"event": "task_finish"}))
            await websocket.close()
        except Exception as e:
            logger.error(f"MINIMAX连接关闭异常: {e}")
    
    async def _convert_audio_to_frames(self, audio_data: bytes) -> AsyncIterable[rtc.AudioFrame]:
        """将音频数据转换为LiveKit AudioFrame"""
        try:
            # 配置pydub使用本地FFmpeg
            self._configure_pydub_ffmpeg()

            # 使用pydub转换MP3到PCM
            audio_segment = AudioSegment.from_mp3(BytesIO(audio_data))

            # 转换为目标格式
            audio_segment = audio_segment.set_frame_rate(self._sample_rate)
            audio_segment = audio_segment.set_channels(self._channel)
            audio_segment = audio_segment.set_sample_width(2)  # 16-bit

            # 获取原始PCM数据
            pcm_data = audio_segment.raw_data

            # 转换为numpy数组
            audio_array = np.frombuffer(pcm_data, dtype=np.int16)

            # 分割为适当大小的帧 (每帧1024个样本)
            frame_size = 1024

            for i in range(0, len(audio_array), frame_size):
                frame_data = audio_array[i:i + frame_size]

                # 如果最后一帧不足1024个样本，用0填充
                if len(frame_data) < frame_size:
                    padded_data = np.zeros(frame_size, dtype=np.int16)
                    padded_data[:len(frame_data)] = frame_data
                    frame_data = padded_data

                # 创建AudioFrame
                frame = rtc.AudioFrame(
                    data=frame_data,
                    sample_rate=self._sample_rate,
                    num_channels=self._channel,
                    samples_per_channel=len(frame_data) // self._channel
                )

                yield frame

        except Exception as e:
            logger.error(f"音频转换失败: {e}")
            return
    
    def synthesize(
        self,
        text: str,
        *,
        conn_options: APIConnectOptions = DEFAULT_API_CONNECT_OPTIONS
    ) -> "MinimaxChunkedStream":
        """合成语音接口"""
        return MinimaxChunkedStream(
            tts=self,
            input_text=text,
            conn_options=conn_options
        )

    def stream(
        self, *, conn_options: APIConnectOptions = DEFAULT_API_CONNECT_OPTIONS
    ) -> "MinimaxSynthesizeStream":
        """创建流式合成接口"""
        return MinimaxSynthesizeStream(tts=self, conn_options=conn_options)

    def _configure_pydub_ffmpeg(self):
        """配置pydub使用本地FFmpeg"""
        try:
            import warnings
            from pathlib import Path
            import os

            # 抑制pydub的FFmpeg警告
            warnings.filterwarnings("ignore", category=RuntimeWarning, module="pydub")

            # 查找本地FFmpeg
            ffmpeg_path = None
            project_root = Path(__file__).parent.parent.parent
            ffmpeg_dir = project_root / "ffmpeg"

            for root, _, files in os.walk(ffmpeg_dir):
                if "ffmpeg.exe" in files:
                    ffmpeg_path = Path(root) / "ffmpeg.exe"
                    break

            if ffmpeg_path and ffmpeg_path.exists():
                # 配置pydub使用本地FFmpeg
                AudioSegment.converter = str(ffmpeg_path)
                AudioSegment.ffmpeg = str(ffmpeg_path)
                AudioSegment.ffprobe = str(ffmpeg_path.parent / "ffprobe.exe")
                logger.info(f"Configured pydub to use local FFmpeg: {ffmpeg_path}")
            else:
                # 设置默认配置，避免警告
                AudioSegment.converter = "ffmpeg"
                AudioSegment.ffmpeg = "ffmpeg"
                logger.info("Using system FFmpeg configuration")
        except Exception as e:
            logger.error(f"Failed to configure FFmpeg: {e}")


class MinimaxChunkedStream(tts.ChunkedStream):
    """MINIMAX TTS 流式输出"""

    def __init__(
        self,
        *,
        tts: MinimaxTTS,
        input_text: str,
        conn_options: APIConnectOptions
    ):
        super().__init__(tts=tts, input_text=input_text, conn_options=conn_options)
        self._minimax_tts = tts

    async def _run(self, output_emitter) -> None:
        """实现ChunkedStream的抽象方法"""
        try:
            # 初始化AudioEmitter
            output_emitter.initialize(
                request_id="minimax_request",
                sample_rate=self._minimax_tts._sample_rate,
                num_channels=self._minimax_tts._channel,
                mime_type="audio/pcm"
            )

            # 建立连接
            websocket = await self._minimax_tts._establish_connection()
            if not websocket:
                raise Exception("Failed to establish WebSocket connection")

            # 开始任务
            if not await self._minimax_tts._start_task(websocket, self.input_text):
                raise Exception("Failed to start synthesis task")

            # 合成音频
            audio_data = await self._minimax_tts._synthesize_text(websocket, self.input_text)

            # 关闭连接
            await self._minimax_tts._close_connection(websocket)

            # 将音频数据转换为AudioFrame并发送
            if audio_data:
                # 将MP3数据转换为AudioFrame (async generator)
                frame_count = 0
                async for frame in self._minimax_tts._convert_audio_to_frames(audio_data):
                    # 直接推送原始音频数据到output_emitter
                    frame_data = frame.data.tobytes()
                    output_emitter.push(frame_data)
                    frame_count += 1

                # 完成音频输出
                output_emitter.flush()
                logger.info(f"Successfully emitted {frame_count} audio frames via output_emitter")

                if frame_count == 0:
                    logger.warning("No audio frames generated")
            else:
                logger.warning("No audio data received from MINIMAX")

        except Exception as e:
            logger.error(f"MINIMAX TTS synthesis failed: {e}")
            raise

    def _convert_mp3_to_frames(self, mp3_data: bytes) -> list[rtc.AudioFrame]:  # 已废弃
        """将MP3数据转换为LiveKit AudioFrame列表"""
        try:
            # 配置pydub使用本地FFmpeg
            self._configure_pydub_ffmpeg()

            # 使用pydub加载MP3数据
            audio_segment = AudioSegment.from_file(
                BytesIO(mp3_data),
                format="mp3"
            )

            # 转换为目标采样率和声道数
            audio_segment = audio_segment.set_frame_rate(self._minimax_tts._sample_rate)
            audio_segment = audio_segment.set_channels(self._minimax_tts._channel)

            # 转换为16位PCM
            audio_segment = audio_segment.set_sample_width(2)  # 16位 = 2字节

            # 获取原始PCM数据
            pcm_data = audio_segment.raw_data

            # 转换为numpy数组
            audio_array = np.frombuffer(pcm_data, dtype=np.int16)

            logger.info(f"Successfully decoded MP3: {len(audio_array)} samples, duration: {len(audio_segment)/1000:.2f}s")

            # 分割为适当大小的帧 (每帧1024个样本)
            frame_size = 1024
            frames = []

            for i in range(0, len(audio_array), frame_size):
                frame_data = audio_array[i:i + frame_size]

                # 如果最后一帧不足1024个样本，用0填充
                if len(frame_data) < frame_size:
                    frame_data = np.pad(frame_data, (0, frame_size - len(frame_data)))

                # 创建AudioFrame
                frame = rtc.AudioFrame.create(
                    sample_rate=self._minimax_tts._sample_rate,
                    num_channels=self._minimax_tts._channel,
                    samples_per_channel=frame_size
                )

                # 将数据复制到frame中
                frame_array = np.frombuffer(frame.data, dtype=np.int16)
                frame_array[:] = frame_data

                frames.append(frame)

            logger.info(f"Converted MP3 to {len(frames)} audio frames")
            return frames

        except Exception as e:
            logger.error(f"Failed to convert MP3 to frames: {e}")
            return []

    def _configure_pydub_ffmpeg(self):
        """配置pydub使用本地FFmpeg"""
        try:
            from pathlib import Path

            # 查找本地FFmpeg
            ffmpeg_path = None
            project_root = Path(__file__).parent.parent.parent
            ffmpeg_dir = project_root / "ffmpeg"

            for root, dirs, files in os.walk(ffmpeg_dir):
                if "ffmpeg.exe" in files:
                    ffmpeg_path = Path(root) / "ffmpeg.exe"
                    break

            if ffmpeg_path and ffmpeg_path.exists():
                # 设置pydub使用本地FFmpeg
                AudioSegment.converter = str(ffmpeg_path)
                AudioSegment.ffmpeg = str(ffmpeg_path)
                AudioSegment.ffprobe = str(ffmpeg_path.parent / "ffprobe.exe")

                # 添加到系统PATH
                ffmpeg_dir = str(ffmpeg_path.parent)
                current_path = os.environ.get("PATH", "")
                if ffmpeg_dir not in current_path:
                    os.environ["PATH"] = f"{ffmpeg_dir};{current_path}"

                logger.info(f"Configured pydub to use local FFmpeg: {ffmpeg_path}")
            else:
                logger.warning("Local FFmpeg not found, using system FFmpeg")
        except Exception as e:
            logger.error(f"Failed to configure FFmpeg: {e}")

    async def _process_audio_stream(self, output_emitter: AudioEmitter, websocket) -> None:
        """处理音频流 - 为ChunkedStream提供的方法"""
        try:
            # 发送文本数据
            await websocket.send(json.dumps({
                "event": "text_data",
                "text": self.input_text
            }))

            # 接收音频数据
            while True:
                response_text = await websocket.recv()
                response = json.loads(response_text)

                if response.get("event") == "audio_data":
                    # 处理音频数据
                    audio_data = response.get("data", "")
                    if audio_data:
                        # 解码base64音频数据
                        import base64
                        audio_bytes = base64.b64decode(audio_data)

                        # 转换MP3到PCM
                        pcm_data = await self._convert_mp3_to_pcm(audio_bytes)
                        if pcm_data:
                            output_emitter.push(pcm_data)

                elif response.get("event") == "audio_end":
                    # 音频结束
                    break
                elif response.get("event") == "error":
                    # 错误处理
                    error_msg = response.get("message", "Unknown error")
                    logger.error(f"MINIMAX TTS error: {error_msg}")
                    break

        except Exception as e:
            logger.error(f"处理音频流错误: {e}")

    async def _convert_mp3_to_pcm(self, mp3_data: bytes) -> Optional[bytes]:
        """将MP3数据转换为PCM格式 - 为ChunkedStream提供的方法"""
        try:
            # 使用pydub转换MP3到PCM
            audio_segment = AudioSegment.from_mp3(BytesIO(mp3_data))

            # 转换为目标格式
            audio_segment = audio_segment.set_frame_rate(self._minimax_tts.sample_rate)
            audio_segment = audio_segment.set_channels(self._minimax_tts.num_channels)
            audio_segment = audio_segment.set_sample_width(2)  # 16-bit

            # 获取原始PCM数据
            return audio_segment.raw_data

        except Exception as e:
            logger.error(f"MP3到PCM转换失败: {e}")
            return None


class MinimaxSynthesizeStream(tts.SynthesizeStream):
    """MINIMAX TTS 流式合成接口 - 完全按照官方标准实现"""

    def __init__(self, *, tts: MinimaxTTS, conn_options: APIConnectOptions):
        super().__init__(tts=tts, conn_options=conn_options)
        self._tts = tts
        self._websocket: Optional[websockets.WebSocketServerProtocol] = None
        self._current_request_id = ""
        self._current_segment_id = ""

    async def _run(self, output_emitter: AudioEmitter) -> None:
        """运行流式合成 - 按照官方AudioEmitter模式"""
        try:
            # 初始化AudioEmitter
            self._current_request_id = utils.shortuuid()
            output_emitter.initialize(
                request_id=self._current_request_id,
                sample_rate=self._tts.sample_rate,
                num_channels=self._tts.num_channels,
                mime_type="audio/pcm",
                stream=True,
            )

            # 建立WebSocket连接
            self._websocket = await self._tts._establish_connection()
            if not self._websocket:
                raise Exception("Failed to establish WebSocket connection")

            # 处理输入文本流
            async for data in self._input_ch:
                if isinstance(data, self._FlushSentinel):
                    # 处理flush信号
                    output_emitter.flush()
                    continue

                # 开始新的段落
                self._current_segment_id = utils.shortuuid()
                output_emitter.start_segment(segment_id=self._current_segment_id)

                # 发送TTS请求
                success = await self._tts._start_task(self._websocket, data)
                if not success:
                    logger.error("Failed to start TTS task")
                    continue

                # 发送文本数据
                await self._websocket.send(json.dumps({
                    "event": "text_data",
                    "text": data
                }))

                # 接收并处理音频数据
                await self._process_audio_response(output_emitter)

                # 结束当前段落
                output_emitter.end_segment()

        except Exception as e:
            logger.error(f"MINIMAX流式合成错误: {e}")
            raise
        finally:
            if self._websocket:
                await self._tts._close_connection(self._websocket)

    async def _process_audio_response(self, output_emitter: AudioEmitter) -> None:
        """处理音频响应"""
        try:
            while True:
                response_text = await self._websocket.recv()
                response = json.loads(response_text)

                if response.get("event") == "audio_data":
                    # 处理音频数据
                    audio_data = response.get("data", "")
                    if audio_data:
                        # 解码base64音频数据
                        import base64
                        audio_bytes = base64.b64decode(audio_data)

                        # 转换MP3到PCM
                        pcm_data = await self._convert_mp3_to_pcm(audio_bytes)
                        if pcm_data:
                            output_emitter.push(pcm_data)

                elif response.get("event") == "audio_end":
                    # 音频结束
                    break
                elif response.get("event") == "error":
                    # 错误处理
                    error_msg = response.get("message", "Unknown error")
                    logger.error(f"MINIMAX TTS error: {error_msg}")
                    break

        except Exception as e:
            logger.error(f"处理音频响应错误: {e}")

    async def _convert_mp3_to_pcm(self, mp3_data: bytes) -> Optional[bytes]:
        """将MP3数据转换为PCM格式"""
        try:
            # 配置pydub使用本地FFmpeg
            self._tts._configure_pydub_ffmpeg()

            # 使用pydub转换MP3到PCM
            audio_segment = AudioSegment.from_mp3(BytesIO(mp3_data))

            # 转换为目标格式
            audio_segment = audio_segment.set_frame_rate(self._tts.sample_rate)
            audio_segment = audio_segment.set_channels(self._tts.num_channels)
            audio_segment = audio_segment.set_sample_width(2)  # 16-bit

            # 获取原始PCM数据
            pcm_data = audio_segment.raw_data
            return pcm_data

        except Exception as e:
            logger.error(f"MP3到PCM转换失败: {e}")
            return None
